.emailInputContainer {
  width: 100%;
  position: relative;

  .emailTagsContainer {
    display: flex;
    width: 100%;
    min-height: 40px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    align-items: center;
    position: relative;
    padding: 10px;
    flex-wrap: wrap;
    overflow: auto;
    gap: 4px;
    transition: border-color 0.2s ease;

    &:focus-within {
      border-color: #459fff;
      box-shadow: 0 0 0 2px rgba(69, 159, 255, 0.2);
    }

    &::-webkit-scrollbar {
      width: 5px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.32);
      border-radius: 50px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &.emptyInput {
      border-color: rgba(255, 255, 255, 0.3);
    }

    .emailInput {
      flex: 1;
      min-width: 100px;
      background: transparent;
      border: none;
      font-family: Inter, sans-serif;
      font-size: 12px;
      font-weight: normal;
      line-height: 1;
      letter-spacing: normal;
      text-align: left;
      color: #fff;
      outline: none;
      padding: 4px;
      caret-color: #459fff;

      &::placeholder {
        font-family: Inter, sans-serif;
        font-size: 12px;
        font-weight: normal;
        line-height: 1;
        letter-spacing: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.4);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .editInput {
      background: transparent;
      border: none;
      font-family: Inter, sans-serif;
      font-size: 12px;
      font-weight: normal;
      line-height: 1;
      letter-spacing: normal;
      text-align: left;
      color: #fff;
      outline: none;
      padding: 0;
      margin: 0;
      width: 100%;
      min-width: 80px;
      caret-color: #459fff;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .emailTag {
      padding: 4px 6px 4px 10px;
      border-radius: 12px;
      background-color: #71737f;
      display: flex;
      align-items: center;
      transition: all 0.2s ease;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: 0.56px;
      text-align: center;
      color: #fff;
      overflow: hidden;
      cursor: pointer;
      user-select: none;

      .emailText {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }

      .removeTag {
        margin-left: 6px;
        cursor: pointer;
        font-family: Syncopate;
        font-size: 10px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.6);
        flex-shrink: 0;
        transition: color 0.2s ease;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }

      &:last-child {
        margin-right: 4px;

        &:after {
          content: '';
          position: absolute;
          right: 10px;
          width: 1px;
          height: 16px;
          background-color: rgba(255, 255, 255, 0.2);
          animation: blink 1s infinite;
          opacity: 0;
        }
      }
    }
  }

  .errorText {
    color: #ff4859;
    font-size: 12px;
    margin-top: 5px;
    font-family: 'Inter', sans-serif;
    position: absolute;
    left: 0;
    top: 100%;

    &.duplicateError {
      color: #ffa500;
      font-weight: 500;
      display: flex;
      align-items: center;

      &:before {
        content: '⚠️';
        margin-right: 5px;
        font-size: 14px;
      }
    }

    &.sameEmailError {
      color: #ff4859;
      font-weight: 500;
      display: flex;
      align-items: center;

      &:before {
        content: '⛔';
        margin-right: 5px;
        font-size: 14px;
      }
    }
  }
}

@keyframes blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}