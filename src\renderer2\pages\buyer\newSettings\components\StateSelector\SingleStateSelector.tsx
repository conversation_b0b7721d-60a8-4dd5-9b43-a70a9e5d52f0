import React, { useState, useEffect, useRef } from 'react';
import styles from './InteractiveStateSelector.module.scss';
import clsx from 'clsx';
import Popover from '@mui/material/Popover';

interface State {
  state_code: string;
}

interface SingleStateSelectorProps {
  states: State[];
  value?: string;
  onChange: (stateCode: string) => void;
  onBlur?: () => void;
  error?: boolean;
  placeholder?: string;
  stateIdToCode?: (stateId: string) => string;
}

const SingleStateSelector: React.FC<SingleStateSelectorProps> = ({
  states,
  value,
  onChange,
  onBlur,
  error,
  placeholder = "Type to filter states...",
  stateIdToCode
}) => {
  const [filterText, setFilterText] = useState('');
  const [filteredStates, setFilteredStates] = useState<State[]>(states);
  const [isFocused, setIsFocused] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);

  const getDisplayValue = () => {
    if (isFocused) return filterText;
    if (!value) return '';
    if (stateIdToCode) {
      return stateIdToCode(value);
    }
    const state = states.find(s => s.state_code === value);
    return state ? state.state_code : value;
  };

  const inputValue = getDisplayValue();

  useEffect(() => {
    setFilteredStates(states);
  }, [states]);

  const getNavigableStates = () => {
    if (!filterText.trim()) {
      return states;
    }
    return states.filter(state =>
      state.state_code.toLowerCase().startsWith(filterText.toLowerCase())
    );
  };

  const navigableStates = getNavigableStates();

  const getStateClass = (stateCode: string, index: number) => {
    const isSelected = value === stateCode;
    const isExactMatch = filterText && stateCode.toLowerCase() === filterText.toLowerCase();
    const isStartsWithMatch = filterText && stateCode.toLowerCase().startsWith(filterText.toLowerCase());
    const navigableIndex = navigableStates.findIndex(state => state.state_code === stateCode);
    const isHovered = navigableIndex === hoveredIndex && navigableIndex >= 0;

    return clsx(
      styles.stateItem,
      isSelected && styles.selected,
      isHovered && styles.hovered,
      !filterText && styles.default,
      isExactMatch && styles.exactMatch,
      isStartsWithMatch && !isExactMatch && styles.startsWithMatch,
      filterText && !isStartsWithMatch && styles.noMatch
    );
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterText(e.target.value);
    setHoveredIndex(-1);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (hoveredIndex >= 0 && hoveredIndex < navigableStates.length) {
        onChange(navigableStates[hoveredIndex].state_code);
        setFilterText('');
        setHoveredIndex(-1);
        setIsFocused(false);
        inputRef.current?.blur();
        return;
      }

      const exactMatches = states.filter(state =>
        state.state_code.toLowerCase() === filterText.toLowerCase()
      );

      if (exactMatches.length === 1) {
        onChange(exactMatches[0].state_code);
        setFilterText('');
        setHoveredIndex(-1);
        setIsFocused(false);
        inputRef.current?.blur();
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (navigableStates.length > 0) {
        const newIndex = hoveredIndex < navigableStates.length - 1 ? hoveredIndex + 1 : 0;
        setHoveredIndex(newIndex);
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (navigableStates.length > 0) {
        const newIndex = hoveredIndex > 0 ? hoveredIndex - 1 : navigableStates.length - 1;
        setHoveredIndex(newIndex);
      }
    }
  };

  const handleStateClick = (stateCode: string) => {
    onChange(stateCode);
    setFilterText('');
    setHoveredIndex(-1);
    setIsFocused(false);
    inputRef.current?.blur();
  };

  const handleStateMouseEnter = (stateCode: string) => {
    const navigableIndex = navigableStates.findIndex(state => state.state_code === stateCode);
    if (navigableIndex >= 0) {
      setHoveredIndex(navigableIndex);
    }
  };

  const handleStateMouseLeave = () => {
    setHoveredIndex(-1);
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    setFilterText('');
  };

  const handlePopoverClose = () => {
    setIsFocused(false);
    setFilterText('');
    setHoveredIndex(-1);
    onBlur?.();
  };

  return (
    <div className={styles.container}>
      <div className={styles.inputContainer}>
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleInputKeyDown}
          onFocus={handleInputFocus}
          onBlur={() => {}} // Prevent premature closing; we handle blur in Popover
          placeholder={placeholder}
          className={clsx(
            styles.input,
            error && styles.inputError,
            isFocused && styles.inputFocused
          )}
        />
      </div>

      <Popover
        open={isFocused}
        anchorEl={inputRef.current}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        disableAutoFocus
        disableEnforceFocus
      >
        <div className={styles.statesGrid}>
          {filteredStates.map((state, index) => (
            <button
              type="button"
              key={state.state_code}
              className={getStateClass(state.state_code, index)}
              onClick={() => handleStateClick(state.state_code)}
              onMouseEnter={() => handleStateMouseEnter(state.state_code)}
              onMouseLeave={handleStateMouseLeave}
            >
              {state.state_code}
            </button>
          ))}
        </div>
      </Popover>
    </div>
  );
};

export default SingleStateSelector;
