import React, { useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { companySchema, settingSchema } from '../schemas';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import clsx from 'clsx';
import { CustomMenu } from '../../CustomMenu';
import { watch } from 'fs';
import { Autocomplete, ClickAwayListener, Dialog, Select, TextField } from '@mui/material';
import { Watch } from '@mui/icons-material';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import CustomAddressComponent from '../components/CustomAddressComponent';
import axios from 'axios';
import { useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { useImmer } from 'use-immer';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import { useQueryClient } from '@tanstack/react-query';
import { prefixUrl, reactQueryKeys, routes } from 'src/renderer2/common';
import { navigatePage } from 'src/renderer2/helper';
import { ReactComponent as IconUpload } from '../../../../assets/New-images/icon-upload.svg';
import { v4 as uuidv4 } from 'uuid';
import DropdownSave from '../components/DropdownSave';
import { EmailTagInputField } from 'src/renderer2/component/EmailTagInput';
import SingleStateSelector from '../components/StateSelector/SingleStateSelector';

interface InputFocusState {
    parentCompanyName: boolean;
    companyDBAName: boolean;
    companyType: boolean;
    billingContactName: boolean;
    billingContactEmail: boolean;
    sendInvoicesTo: boolean;
    sendRemittancesTo: boolean;
    companyAddressLine1: boolean;
    companyAddressLine2: boolean;
    companyAddressCity: boolean;
    companyAddressState: boolean;
    companyAddressZip: boolean;
}

const CompanyTab: React.FC<{ yourCompanyList: any, setActiveTab: any, setSaveFunctions: any}> = ({ yourCompanyList, setActiveTab , setSaveFunctions }) => {
    const {
        register,
        handleSubmit,
        clearErrors,
        setError,
        setValue,
        reset,
        watch,
        control,
        getValues,
        trigger,
        resetField,
        formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
        getFieldState,
      } = useForm({
        resolver: yupResolver(companySchema),
        mode: 'onBlur',
      });
    
    const selectCompanyHQAddressRef = useRef(null);
    const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
        parentCompanyName: false,
        companyDBAName: false,
        companyType: false,
        billingContactName: false,
        billingContactEmail: false,
        sendInvoicesTo: false,
        sendRemittancesTo: false,
        companyAddressLine1: false,
        companyAddressLine2: false,
        companyAddressCity: false,
        companyAddressState: false,
        companyAddressZip: false,
    });
    const {referenceData , setShowLoader, userData}: any = useGlobalStore();
    const [states, setStates] = useState<any[]>([]);
    const [yourCompanyValue, setYourCompanyValue] = useImmer(null);
    const [yourCompanyInput, setYourCompanyInput] = useState("");
    const { mutateAsync: saveUserSettings } = useSaveUserSettings();
    const { buyerSetting }: any = useBuyerSettingStore();
    const queryClient = useQueryClient();

    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const companyW9FormRef = useRef<HTMLInputElement>(null);
    const isButtonDisabled = !isValid || isSubmitting || !isDirty;

    useEffect(() => {
        if(buyerSetting) {
            setValue('parentCompanyName', buyerSetting?.company_name || '');
            setValue('companyDBAName', buyerSetting?.client_company || '');
            setValue('companyType', buyerSetting?.company_type || '');
            setValue('companyAddress', {
              line1: buyerSetting?.company_address?.line1 || '',
              line2: buyerSetting?.company_address?.line2 || '',
              city: buyerSetting?.company_address?.city || '',
              state: buyerSetting?.company_address?.state_id || '',
              stateCode: buyerSetting?.company_address?.state_code || '',
              zip: buyerSetting?.company_address?.zip || '',
            });
            setValue('billingContactName', buyerSetting?.billing_contact_name || '');
            setValue('billingContactEmail', buyerSetting?.billing_email_id || '');
            setValue('sendInvoicesTo', buyerSetting?.send_invoices_to || '');
            setValue('sendRemittancesTo', '<EMAIL>');
        
        }
    }, [buyerSetting]);


    useEffect(() => {
        setSaveFunctions({
            onSave: () => handleSubmit(handleSaveCompany)(),
            onSaveAndNext: () => handleSubmit(handleSaveAndNext)(),
            onSaveAndExit: () => handleSubmit(handleSaveAndExit)(),
            isDisabled: isButtonDisabled,
        });
    }, [isButtonDisabled, handleSubmit]);


    useEffect(() => {   
        setTimeout(() => {
            const parentCompanyNameInput = document.getElementById('parentCompanyName');
            if (parentCompanyNameInput) {
                parentCompanyNameInput.focus();
            }
        }, 100)
    }, []);

    console.log( watch() , isValid );

    useEffect(() => {
        if(referenceData?.ref_states) {
            setStates(referenceData?.ref_states)
        }
    }, [referenceData])

    useEffect(() => {
        if(watch('companyDBAName') !== "") {
            setYourCompanyInput(watch('companyDBAName') ?? "")
        }
    }, [watch('companyDBAName')])

    const handleInputFocus = (inputName: keyof InputFocusState): void => {
        setIsInputFocused((prevState) => ({
            ...prevState,
            [inputName]: true,
        }));
    };

    const handleInputBlur = (inputName: keyof InputFocusState): void => {
        setIsInputFocused((prevState) => ({
            ...prevState,
            [inputName]: false,
        }));
    };

    const companyTypes = [
        { title: 'Fabricator', value: 'Fabricator' },
        { title: 'Constructor', value: 'Constructor' },
        { title: 'Distributor', value: 'Distributor' },
        { title: 'OEM', value: 'OEM' },
    ];

    const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
        try {
            if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
                const payload = {
                data: {
                    state_id: Number(getValues(stateCode)),
                    zip_code: parseInt(getValues(zipCode)),
                },
            };
            const checkStateZipResponse = await axios.post(
                import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
                payload
            );
            if (checkStateZipResponse.data.data === true) {
                clearErrors([stateCode, zipCode]);
                return true;
            } else {
                setError(stateCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
                setError(zipCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
                return false;
            }
        }
     } catch (error) {
            console.error(error)
        }
    };

    useEffect(() => {
        handleStateZipValidation('companyAddress.zip', 'companyAddress.state');
    }, [watch('companyAddress.zip'), watch('companyAddress.state')])


    const handleSaveCompany = async (data: any) => {
        try{
            if(isValid){
                setShowLoader(true)
                const payload = {
                    company_name: data.parentCompanyName,
                    client_company: data.companyDBAName,
                    company_type: data.companyType,
                    company_address: {
                        line1: data.companyAddress.line1,
                        line2: data.companyAddress.line2?.trim() || null,
                        city: data.companyAddress.city,
                        state_id: Number(data.companyAddress.state),
                        zip: data.companyAddress.zip
                    },
                    billing_contact_name: data.billingContactName,
                    billing_email_id: data.billingContactEmail,
                    send_invoices_to: data.sendInvoicesTo,
                    send_remittances_to: data.sendRemittancesTo,
                }
                console.log('payload' , payload)
                await saveUserSettings({route: 'user/settings/company', data: payload})
                queryClient.invalidateQueries([reactQueryKeys.getBuyingPreference])
                reset(data); 
                setShowLoader(false)
            }
        }catch(err){
            console.error(err)
        }
    }

    const handleSaveAndNext = async (data: any) => {
        await handleSaveCompany(data);
        // Navigate to next tab or step
        setActiveTab && setActiveTab('USER'); // Assuming next tab index is 1
    }

    const handleSaveAndExit = async (data: any) => {
        await handleSaveCompany(data);
        navigatePage(location.pathname, { path: routes.homePage })

        // Close the modal or navigate away
        // You can add your exit logic here
    }

    const handleDropdownToggle = () => {
        if (!isValid || isSubmitting || !isDirty) return;
        setIsDropdownOpen(!isDropdownOpen);
    }

    const handleClickAway = () => {
        setIsDropdownOpen(false);
    }

    const irsW9FormEditHandler = () => {
        companyW9FormRef?.current?.click();
    }

    const uploadW9Form = async (event: any) => {
        const file = event.target.files[0];
        console.log('file <<<<<' , file)

        if (event.target.files.length !== 0) {
            let index = file.name.length - 1;
            for (; index >= 0; index--) {
                if (file.name.charAt(index) === '.') {
                    break;
                }
            }
            const ext = file.name.substring(index + 1, file.name.length);

            const objectKey = import.meta.env.VITE_ENVIRONMENT + '/' + userData.data.id + '/' + prefixUrl.irsW9Prefix + '-' + uuidv4() + '.' + ext;

            const payload = {
                data: {
                    "bucket_name": import.meta.env.VITE_S3_UPLOAD_SETTINGS_IRS_W9_BUCKET_NAME,
                    "object_key": objectKey,
                    "expire_time": 300

                }
            }
            let setIRSUrl = 'https://' + payload.data.bucket_name + ".s3.amazonaws.com/" + payload.data.object_key;
            // axios.post(import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', payload)
            //     .then(response => {
            //         const signedUrl = response.data.data;
            //         axios.put(signedUrl, file)
            //             .then(response => {
            //                 if (response.status === 200) {
            //                     setValue('companyW9Form.cerificate_url_s3', setIRSUrl)

            //                 }
            //             })
            //             .catch(error => {
            //                 console.error(error);
            //                 setShowLoader(false);
            //             }
            //             );
            //     })
            //     .catch(error => {
            //         console.error(error);
            //         setShowLoader(false);
            //     }
            //     );

        }
    }

    console.log("errors", errors ,watch());

    return (
        <div className={styles.tabContent} ref={selectCompanyHQAddressRef}>
            <div className={styles.formContainer}>
                {/* PARENT COMPANY NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.parentCompanyName && styles.focusLbl)} htmlFor="parentCompanyName">
                            PARENT COMPANY NAME
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <span className={clsx(styles.inputCreateAccount)}
                        id='parentCompanyName'
                        tabIndex={0} 
                        onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                parentCompanyName: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                parentCompanyName: false,
                            }));
                        }}
                        >{watch('parentCompanyName')}</span>
                    </span>
                </div>

                {/* COMPANY D.B.A. NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyDBAName && styles.focusLbl)} htmlFor="companyDBAName">
                            COMPANY D.B.A. NAME
                        </label>
                    </span>
                    <span className={styles.col1}>
                    <span className={clsx(styles.autocompleteContainer,'autocompleteContainer', errors.companyDBAName && styles.borderOfError)}>
                        
                                <Controller
                                    name="companyDBAName"
                                    control={control}
                                    render={({ field: { ...rest } }) => (
                                        <Autocomplete
                                            freeSolo
                                            value={yourCompanyValue}
                                            onChange={(event, value) => {
                                                setYourCompanyValue(value);
                                                rest.onChange(value ?? null);
                                            }}
                                            onFocus={() => {
                                                setIsInputFocused((prevState) => ({
                                                    ...prevState,
                                                    companyDBAName: true,
                                                }));
                                            }}
                                            inputValue={yourCompanyInput}
                                            className={'companySelectDropdown'}
                                            onInputChange={(event, newInputValue) => {
                                                setYourCompanyInput(newInputValue);
                                                rest.onChange(newInputValue)
                                            }}
                                            onBlur={() => {
                                                setIsInputFocused((prevState) => ({
                                                    ...prevState,
                                                    companyDBAName: false,
                                                }));
                                                handleInputBlur('companyDBAName')
                                                trigger('companyDBAName')
                                            }}
                                            classes={{
                                                paper: clsx(styles.autocompleteDropdown,styles.autocompleteDBA),
                                                listbox: styles.autocompleteListbox,
                                                option: styles.autocompleteOption,
                                                }}
                                            id="controllable-states-demo"
                                            disablePortal={true}
                                            options={yourCompanyList?.length ? yourCompanyList : []}
                                            sx={{ width: '100%' }}
                                            renderInput={(params) => (
                                            <TextField className={styles.companyInput} {...params}  />
                                            )}
                                            getOptionLabel={(item) => {
                                                return item ?? "";
                                            }}
                                        />
                                    )}
                                />
                            </span>
                    </span>
                </div>

                {/* COMPANY TYPE */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyType && styles.focusLbl)} htmlFor="companyType">
                            COMPANY TYPE
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <CustomMenu
                            onfocus={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    companyType: true,
                                }));
                            }}
                            onBlur={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    companyType: false,
                                }));
                            }}
                            control={control}
                            name={'companyType'}
                            // placeholder={'Company Type'}
                            MenuProps={{
                                classes: {
                                    paper: styles.Dropdownpaper,
                                    list: styles.muiMenuList,
                                    select: styles.selectClassName,
                                },
                                id: 'companyTypeMenu'
                            }}
                            className={styles.selectDropdown}
                            placeholderClass={styles.placeholderTxt}
                            IconComponent={DropdownIcon}
                            items={companyTypes}
                            renderValue={(value: string) => {
                                const selectedType = companyTypes.find(type => type.value === value);
                                return (
                                    <span>
                                        {selectedType?.title}
                                    </span>
                                );
                            }}

                        />
                    </span>
                </div>

                {/* COMPANY HQ ADDRESS */}
                <div className={ clsx(styles.formGroupInput, styles.companyHQAddressContainer)}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyAddressLine1 && styles.focusLbl)} htmlFor="companyAddress">
                            COMPANY HQ ADDRESS
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <div className={clsx(styles.customAddressContainer)}>
                            <InputWrapper>
                                <CustomTextField
                                    className={clsx(styles.inputCreateAccount, errors?.companyAddress?.line1 && styles.error)}
                                    type='text'
                                    register={register('companyAddress.line1')}
                                    placeholder='Line 1'
                                    onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                        register('companyAddress.line1').onBlur(e);
                                        handleInputBlur('companyAddressLine1')
                                    }}
                                    onFocus={() => handleInputFocus('companyAddressLine1')}
                                    errorInput={errors?.companyAddress?.line1}
                                />
                            </InputWrapper>
                            <InputWrapper>
                                <CustomTextField
                                    className={clsx(styles.inputCreateAccount, errors?.companyAddress?.line2 && styles.error)}
                                    type='text'
                                    // autoFocus={true}
                                    register={register('companyAddress.line2')}
                                    placeholder='Line 2'
                                    onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                        register('companyAddress.line2').onBlur(e);
                                        handleInputBlur('companyAddressLine2')
                                    }}
                                    onFocus={() => handleInputFocus('companyAddressLine2')}
                                    errorInput={errors?.companyAddress?.line2}
                                />
                            </InputWrapper>

                            <span className={styles.zipInputContainer}>
                                <span className={styles.col1}>
                                    <InputWrapper>
                                        <CustomTextField
                                            className={clsx(styles.inputCreateAccount, errors?.companyAddress?.city && styles.error)}
                                            type='text'
                                            register={register('companyAddress.city')}
                                            placeholder='City'
                                            onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                                register('companyAddress.city').onBlur(e);
                                                handleInputBlur('companyAddressCity')
                                            }}
                                            onFocus={() => handleInputFocus('companyAddressCity')}
                                            errorInput={errors?.companyAddress?.city}
                                        />
                                    </InputWrapper>
                                </span>
                                <span className={clsx(styles.inputSection, styles.yourLocationAdressState, styles.col2, styles.bdrRadius0, styles.bdrRight0)}>
                                    {/* <CustomMenu
                                        control={control}
                                        name={"companyAddress.state"}
                                        placeholder={'State'}
                                        MenuProps={{
                                            classes: {
                                                paper: clsx(styles.Dropdownpaper, styles.Dropdownpaper1),
                                                list: styles.muiMenuList,
                                                select: styles.selectClassName,
                                            },
                                        }}
                                        className={clsx(styles.selectDropdown, styles.selectState)}
                                        items={states.map((x: any) => ({ title: x.code, value: x.id }))}
                                        onChange={(e: any) => {
                                            states.map((item: any) => {
                                                if (item.id === e.target.value) {
                                                    setValue("companyAddress.stateCode", item.code)
                                                }
                                            })
                                        }}
                                    /> */}
                                    <Controller
                                        name="companyAddress.state"
                                        control={control}
                                        render={({ field }) => (
                                            <>
                                            <SingleStateSelector
                                                states={states.map((state: any) => ({ state_code: state.code }))}
                                                value={field.value}
                                                onChange={(stateCode) => {
                                                    const selectedState = states.find((state: any) => state.code === stateCode);
                                                    field.onChange(selectedState?.id || '');
                                                    setValue('companyAddress.stateCode', selectedState?.code || '');
                                                }}
                                                onBlur={field.onBlur}
                                                error={!!errors?.companyAddress?.state}
                                                placeholder="State"
                                                stateIdToCode={(stateId) => {
                                                    const state = states.find((s: any) => s.id === stateId);
                                                    return state ? state.code : stateId;
                                                }}
                                                />
                                                </>
                                        )}
                                    />

                                </span>
                                <span className={styles.col3}>
                                    <InputWrapper>
                                        <CustomTextField
                                            className={clsx(styles.inputCreateAccount, (errors?.companyAddress?.zip || errors?.companyAddress?.state) && styles.error)}
                                            type='text'
                                            maxLength={5}
                                            register={register('companyAddress.zip')}
                                            placeholder='Zip Code'
                                            onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                                register('companyAddress.zip').onBlur(e);
                                                handleInputBlur('companyAddressZip');
                                            }}
                                            onFocus={() => handleInputFocus('companyAddressZip')}
                                            errorInput={errors?.companyAddress?.zip || errors?.companyAddress?.state}
                                            mode="wholeNumber"
                                        />
                                    </InputWrapper>
                                </span>
                            </span>

                        </div>
                    </span>
                </div>

                   {/* COMPANY W9 FORM */}
                   <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label htmlFor="companyW9Form">
                            COMPANY W9 FORM
                        </label>
                    </span>
                    <span className={styles.col1} >
                        {watch('companyW9Form.cerificate_url_s3') ?
                            <span>
                                <a className={styles.viewBtn} href={watch('companyW9Form.cerificate_url_s3')} >View</a><span className={styles.orText}>or</span><button className={styles.viewBtn} onClick={irsW9FormEditHandler}>Edit </button>
                            </span>
                            :
                            <label>
                                <button onClick={irsW9FormEditHandler} className={styles.uploadText}>
                                    <span>Upload W-9</span>
                                    <span className={styles.uploadIcon}>
                                        <IconUpload />
                                    </span>
                                </button>
                            </label>
                        }
                        <input className={styles.uploadFileInput} {...register(`companyW9Form`)} type='file'  onChange={(e) => { uploadW9Form(e); register(`companyW9Form`).onChange(e) }} ref={companyW9FormRef} />
                    </span>
                </div>


                {/* BILLING CONTACT NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.billingContactName && styles.focusLbl)} htmlFor="billingContactName">
                            AP CONTACT NAME
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <InputWrapper>
                            <CustomTextField
                                className={clsx(styles.inputCreateAccount, errors?.billingContactName && styles.error)}
                                type='text'
                                register={register("billingContactName")}
                                placeholder=''
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    register("billingContactName").onBlur(e);
                                    // handleInputBlur('billingContactName')
                                }}
                                onFocus={() => handleInputFocus('billingContactName')}
                                errorInput={errors?.billingContactName}
                            />
                        </InputWrapper>
                    </span>
                </div>

                {/* BILLING CONTACT EMAIL */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.billingContactEmail && styles.focusLbl)} htmlFor="billingContactEmail">
                            AP CONTACT EMAIL
                        </label>
                    </span>
                    <span className={styles.col1}>
                        {/* <InputWrapper>
                            <CustomTextField
                                className={clsx(styles.inputCreateAccount, errors?.billingContactEmail && styles.error)}
                                type='email'
                                register={register("billingContactEmail")}
                                placeholder=''
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    register("billingContactEmail").onBlur(e);
                                    handleInputBlur('billingContactEmail')
                                }}
                                onFocus={() => handleInputFocus('billingContactEmail')}
                                errorInput={errors?.billingContactEmail}
                            />
                        </InputWrapper> */}
                          <EmailTagInputField
                            value={watch('billingContactEmail') ? watch('billingContactEmail').split(',').filter(Boolean) : []}
                            onChange={(emails) => {
                                setValue('billingContactEmail', emails.join(','));
                                // Trigger validation after setting the value
                                trigger('billingContactEmail');
                            }}
                            placeholder="Enter email addresses..."
                            maxEmails={5}
                            register={register("billingContactEmail")}
                            error={errors?.billingContactEmail?.message}
                            onBlur={() => trigger('billingContactEmail')}
                            control={control}
                        />
                    </span>
                </div>

                {/* SEND INVOICES TO */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.sendInvoicesTo && styles.focusLbl)} htmlFor="sendInvoicesTo">
                            SEND INVOICES TO
                        </label>
                    </span>
                    <span className={styles.col1}>
                        {/* <InputWrapper>
                            <CustomTextField
                                className={clsx(styles.inputCreateAccount, styles.sendInvoiceEmailInput,errors?.sendInvoicesTo && styles.error)}
                                type='email'
                                register={register("sendInvoicesTo")}
                                placeholder=''
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    register("sendInvoicesTo").onBlur(e);
                                    handleInputBlur('sendInvoicesTo')
                                }}
                                onFocus={() => handleInputFocus('sendInvoicesTo')}
                                errorInput={errors?.sendInvoicesTo}
                            />
                        </InputWrapper> */}
                        <EmailTagInputField
                            value={watch('sendInvoicesTo') ? watch('sendInvoicesTo').split(',').filter(Boolean) : []}
                            onChange={(emails) => {
                                setValue('sendInvoicesTo', emails.join(','));
                                // Trigger validation after setting the value
                                trigger('sendInvoicesTo');
                            }}
                            placeholder="Enter email addresses..."
                            maxEmails={5}
                            register={register("sendInvoicesTo")}
                            error={errors?.sendInvoicesTo?.message}
                            onBlur={() => trigger('sendInvoicesTo')}
                            control={control}
                        />

                    </span>
                </div>

                {/* SEND REMITTANCES TO */}
                <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.sendRemittancesTo && styles.focusLbl)} htmlFor="sendRemittancesTo">
                            SEND REMITTANCES TO
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <span className={clsx(styles.inputCreateAccount , styles.arBryzosCom)} tabIndex={0} onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendRemittancesTo: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendRemittancesTo: false,
                            }));
                        }}
                        onKeyDown={(e) => {
                            if(e.key === 'Tab' && !e.shiftKey){
                                setActiveTab('USER');
                            }
                        }}
                        >
                            {watch('sendRemittancesTo') ?? "<EMAIL>"}
                        </span>
                    </span>
                </div>

            
            </div>
        </div>
    );
};

export default CompanyTab; 
